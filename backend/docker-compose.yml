version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: testangel-postgres
    environment:
      POSTGRES_DB: testAngel
      POSTGRES_USER: stepup
      POSTGRES_PASSWORD: stepup
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - testangel-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U stepup -d testAngel"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Spring Boot Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: testangel-backend
    environment:
      SPRING_DATASOURCE_URL: *****************************************
      SPRING_DATASOURCE_USERNAME: stepup
      SPRING_DATASOURCE_PASSWORD: stepup
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      JWT_SECRET: 2f9c5a2c12b3cb2fed2a74ce3310bdf8adb86c8a50df44ddf76f8c2d0df6316b
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - testangel-network
    volumes:
      - ./uploads:/app/uploads
      - ./temp_uploads:/app/temp_uploads

volumes:
  postgres_data:

networks:
  testangel-network:
    driver: bridge 