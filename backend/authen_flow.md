graph TD
    A[User clicks Google Login] --> B[Frontend redirects to Backend OAuth endpoint]
    B --> C[Spring Security OAuth2 Client]
    C --> D[Redirect to Google Authorization Server]
    D --> E[User authorizes on Google]
    E --> F[Google redirects back with authorization code]
    F --> G[CustomOAuth2UserService processes user info]
    G --> H[UserService finds or creates user]
    H --> I[OAuth2AuthenticationSuccessHandler generates JWT]
    I --> J[Redirect to Frontend with JWT token]
    J --> K[Frontend stores JWT and manages session]