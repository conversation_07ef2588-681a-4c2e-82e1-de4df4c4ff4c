# Test Angel Backend

## Prerequisites
- Java 17 or higher (for local development)
- <PERSON><PERSON> (or use included <PERSON><PERSON> wrapper)
- Docker & Docker Compose (for containerized deployment)

## Running with Docker (Recommended)

### Option 1: Using Docker Compose (Full Stack)
```bash
# Navigate to backend directory
cd backend

# Start both database and application
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Stop and remove volumes (caution: deletes database data)
docker-compose down -v
```

### Option 2: Docker Build & Run Manually
```bash
# Navigate to backend directory
cd backend

# Start PostgreSQL first
docker run -d --name testangel-postgres \
  -e POSTGRES_DB=testAngel \
  -e POSTGRES_USER=stepup \
  -e POSTGRES_PASSWORD=stepup \
  -p 5432:5432 \
  postgres:15-alpine

# Build application image
docker build -t testangel-backend .

# Run application container
docker run -d --name testangel-app \
  --link testangel-postgres:postgres \
  -e SPRING_DATASOURCE_URL=***************************************** \
  -e SPRING_DATASOURCE_USERNAME=stepup \
  -e SPRING_DATASOURCE_PASSWORD=stepup \
  -p 8080:8080 \
  testangel-backend
```

## Database Setup (Local Development)
```bash
# Run PostgreSQL using Docker
docker run -d --name some-postgres -e POSTGRES_DB=testAngel -e POSTGRES_USER=stepup -e POSTGRES_PASSWORD=stepup -p 5432:5432 postgres
```

## Running Locally (Development)

### Windows (PowerShell/Command Prompt)
```cmd
# Navigate to backend directory
cd backend

# Run using Maven wrapper
.\mvnw.cmd spring-boot:run

# Alternative: Using Maven directly (if installed)
mvn spring-boot:run
```

### Mac/Linux (Terminal)
```bash
# Navigate to backend directory
cd backend

# Make Maven wrapper executable (first time only)
chmod +x ./mvnw

# Run using Maven wrapper
./mvnw spring-boot:run

# Alternative: Using Maven directly (if installed)
mvn spring-boot:run
```

## Verification
- Application will start on: `http://localhost:8080`
- Health check: `http://localhost:8080/actuator/health`
- API Documentation (Swagger): `http://localhost:8080/swagger-ui/index.html`

## Docker Commands Reference
```bash
# Check running containers
docker ps

# View application logs
docker logs testangel-backend

# View database logs
docker logs testangel-postgres

# Execute commands in container
docker exec -it testangel-backend sh
docker exec -it testangel-postgres psql -U stepup -d testAngel

# Remove all containers and images
docker-compose down
docker rmi testangel-backend
```

## Troubleshooting
- Ensure Docker is running
- For local development: Ensure PostgreSQL is running on port 5432
- Check Java version: `java -version` (should be 17+)
- Check if port 8080 is available
- For Windows: Use `.\mvnw.cmd` instead of `./mvnw`
- Docker issues: Check `docker-compose logs` for error details