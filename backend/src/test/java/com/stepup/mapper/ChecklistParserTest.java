package com.stepup.mapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

class ChecklistParserTest {

    private ChecklistParser checklistParser;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        checklistParser = new ChecklistParser(objectMapper);
    }

    @Test
    void testParseTestCasesToJson() throws Exception {
        String testInput = """
### **Test Case: TC_IAP_028 - Verify API Call to Apple App Store upon Selecting Payment Method**

**Description:**  
This test case verifies that selecting the specific payment method triggers the correct API call or interface invocation to the Apple App Store.

**Source (from Checklist):**  
"28. <PERSON><PERSON>c minh chọn phương thức này dẫn đến việc gọi API/interface của Apple App Store."

---

**Preconditions:**  
* User has successfully logged into the application on an Apple device or simulator.  
* User has navigated to the payment or checkout section where payment methods are available.

**Steps:**  
1.  **Action:** Select the Apple App Store payment method from the list of available payment options.  
    **Expected Result:** The system initiates a call to the Apple App Store API or interface.  
    **Test Data:**  
        * Payment Method: "Apple App Store"  

2.  **Action:** Monitor network traffic or logs to verify the API call is made.  
    **Expected Result:** The API call is successfully sent without errors.  
    **Test Data:** N/A

---

**Additional Notes (if any):**  
* Use network debugging tools to capture API calls.  
* Test on multiple versions of iOS if possible.
""";

        String result = checklistParser.parseTestCasesToJson(testInput);
        JsonNode jsonNode = objectMapper.readTree(result);
        
        assertNotNull(jsonNode);
        assertTrue(jsonNode.has("testCases"));
        assertTrue(jsonNode.has("metadata"));
        
        JsonNode testCases = jsonNode.get("testCases");
        assertEquals(1, testCases.size());
        
        JsonNode firstTestCase = testCases.get(0);
        assertEquals("TC_IAP_028", firstTestCase.get("id").asText());
        assertEquals("Verify API Call to Apple App Store upon Selecting Payment Method", 
                     firstTestCase.get("title").asText());
        assertTrue(firstTestCase.get("description").asText().contains("triggers the correct API call"));
        
        JsonNode steps = firstTestCase.get("steps");
        assertEquals(2, steps.size());
        
        System.out.println("Generated JSON:");
        System.out.println(result);
    }
} 