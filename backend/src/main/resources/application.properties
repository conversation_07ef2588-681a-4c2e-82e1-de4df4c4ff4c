spring.application.name=demo

# Security Configuration
spring.security.user.name=admin
spring.security.user.password=admin
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration

# Database Configuration
spring.datasource.url=******************************************
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.username=stepup
spring.datasource.password=stepup
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# JWT Configuration
# Base64 encoded secret (at least 32 bytes when decoded for HS256)
app.jwt.secret=${JWT_SECRET:2f9c5a2c12b3cb2fed2a74ce3310bdf8adb86c8a50df44ddf76f8c2d0df6316b}
app.jwt.expiration-ms=86400000

# OpenAI Configuration
openai.api.key=hi
openai.api.model=gpt-4o-mini
openai.api.timeout=60
openai.api.max-tokens=2000
openai.api.temperature=0.7
openai.api.url=https://api.openai.com/v1/chat/completions

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Swagger OpenAPI Configuration
swagger.openapi.local-url=http://localhost:8080
swagger.openapi.dev-url=
swagger.openapi.staging-url=
swagger.openapi.prod-url=