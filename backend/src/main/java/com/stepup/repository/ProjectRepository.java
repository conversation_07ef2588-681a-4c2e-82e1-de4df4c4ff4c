package com.stepup.repository;

import com.stepup.model.project.Project;
import com.stepup.model.project.ProjectStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectRepository extends JpaRepository<Project, String> {

    List<Project> findByCreatedBy(String createdBy);

    List<Project> findByStatus(ProjectStatus status);

    List<Project> findByCreatedByAndStatus(String createdBy, ProjectStatus status);
}