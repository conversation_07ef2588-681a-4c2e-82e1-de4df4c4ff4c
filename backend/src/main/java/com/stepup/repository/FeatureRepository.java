package com.stepup.repository;

import com.stepup.model.project.Feature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FeatureRepository extends JpaRepository<Feature, String> {

    List<Feature> findByProjectIdAndIsActiveTrue(String projectId);

    List<Feature> findByProjectId(String projectId);

    @Query("SELECT f FROM Feature f WHERE f.projectId = :projectId AND f.status = :status AND f.isActive = true")
    List<Feature> findByProjectIdAndStatusAndIsActiveTrue(@Param("projectId") String projectId,
                                                          @Param("status") Feature.FeatureStatus status);

    long countByProjectIdAndIsActiveTrue(String projectId);
}