package com.stepup.repository;

import com.stepup.model.ai.AIPromptBucket;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AIPromptBucketRepository extends JpaRepository<AIPromptBucket, String> {
    Optional<AIPromptBucket> findByName(String genChecklist);
    // Custom query methods can be added here
} 