package com.stepup.repository;

import com.stepup.model.ai.AIProviderTokens;
import com.stepup.model.ai.ModelAIProvider;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AIProviderTokensRepository extends JpaRepository<AIProviderTokens, String> {
    Optional<AIProviderTokens> findByProvider(ModelAIProvider provider);
} 