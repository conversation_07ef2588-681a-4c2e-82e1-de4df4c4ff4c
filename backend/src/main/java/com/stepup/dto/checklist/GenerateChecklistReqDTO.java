package com.stepup.dto.checklist;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateChecklistReqDTO {
    @JsonProperty("feature_id")
    private String featureId;

    @JsonProperty("text")
    private String text;
}
