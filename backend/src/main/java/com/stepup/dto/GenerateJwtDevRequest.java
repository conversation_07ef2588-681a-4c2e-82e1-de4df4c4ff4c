package com.stepup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GenerateJwtDevRequest {
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("role")
    private String role;
    
    @JsonProperty("user_id")
    private String userId;
    
    public GenerateJwtDevRequest() {}
    
    public GenerateJwtDevRequest(String email, String role, String userId) {
        this.email = email;
        this.role = role;
        this.userId = userId;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
} 