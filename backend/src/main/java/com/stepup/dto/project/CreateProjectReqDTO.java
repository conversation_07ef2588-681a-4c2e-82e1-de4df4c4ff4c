package com.stepup.dto.project;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateProjectReqDTO {
    @NotBlank(message = "Project name is required")
    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;
}