package com.stepup.model.test;

import com.stepup.model.ModifierTrackingEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import java.time.LocalDateTime;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "test_results")
public class TestResult extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @NotNull
    @Column(name = "session_id", nullable = false)
    private String sessionId;

    @NotNull
    @Column(name = "item_id", nullable = false)
    private String itemId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TestResultStatus status = TestResultStatus.NOT_TESTED;

    @Column(name = "actual_result")
    private String actualResult;

    @Column(name = "notes")
    private String notes;

    @Column(name = "screenshot_url", length = 500)
    private String screenshotUrl;

    @Column(name = "bug_report_url", length = 500)
    private String bugReportUrl;

    @NotNull
    @Column(name = "tested_by", nullable = false)
    private String testedBy;

    @Column(name = "tested_at")
    private LocalDateTime testedAt;
}