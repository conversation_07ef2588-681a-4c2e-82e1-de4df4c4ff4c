package com.stepup.model.test;

import com.stepup.model.ModifierTrackingEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import java.time.LocalDateTime;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "test_sessions")
public class TestSession extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @NotNull
    @Column(name = "project_id", nullable = false)
    private String projectId;

    @NotNull
    @Column(name = "template_id", nullable = false)
    private String templateId;

    @NotNull
    @Column(name = "session_name", nullable = false)
    private String sessionName;

    @NotNull
    @Column(name = "tester_id", nullable = false)
    private String testerId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TestSessionStatus status = TestSessionStatus.PENDING;

    @Column(name = "start_date")
    private LocalDateTime startDate;

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Column(name = "notes")
    private String notes;
}