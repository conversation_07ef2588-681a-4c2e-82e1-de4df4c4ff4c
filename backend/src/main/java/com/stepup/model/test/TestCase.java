package com.stepup.model.test;

import com.stepup.model.ModifierTrackingEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "test_cases", indexes = {
        @Index(name = "idx_checklist_item_id", columnList = "checklist_item_id"),
        @Index(name = "tc_idx_status", columnList = "status"),
        @Index(name = "tc_idx_priority", columnList = "priority")
})
public class TestCase extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @NotNull
    @Column(name = "checklist_item_id", nullable = false)
    private String checklistItemId;

    @NotNull
    @Column(name = "title", length = 500, nullable = false)
    private String title;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "preconditions", columnDefinition = "TEXT")
    private String preconditions;

    @Column(name = "test_steps", columnDefinition = "TEXT")
    private String testSteps;

    @Column(name = "expected_result", columnDefinition = "TEXT")
    private String expectedResult;

    @Column(name = "actual_result", columnDefinition = "TEXT")
    private String actualResult;

    @NotNull
    @Column(name = "priority", nullable = false)
    @Builder.Default
    private Integer priority = 1;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private TestCaseStatus status = TestCaseStatus.PENDING;


    @Column(name = "execution_time_minutes")
    private Integer executionTimeMinutes;

    @Column(name = "assigned_to")
    private String assignedTo;

    public enum TestCaseStatus {
        PENDING, IN_PROGRESS, PASSED, FAILED, BLOCKED, SKIPPED
    }
}