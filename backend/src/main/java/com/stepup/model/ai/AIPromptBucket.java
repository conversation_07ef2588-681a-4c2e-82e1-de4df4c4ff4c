package com.stepup.model.ai;

import com.stepup.model.ModifierTrackingEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "ai_prompt_bucket")
public class AIPromptBucket extends ModifierTrackingEntity {
    @Id
    @Column(name = "id", columnDefinition = "VARCHAR(255)")
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "prompt", nullable = false, columnDefinition = "TEXT")
    private String prompt;

    @Column(name = "model", nullable = false)
    private String model;

    @Column(name = "top_p")
    private String topP;

    @Column(name = "temperature")
    private String temperature;

    @Column(name = "max_token")
    private String maxToken;

    @Column(name = "response_type")
    private String responseType;

    @Column(name = "response_language")
    private String responseLanguage;
} 