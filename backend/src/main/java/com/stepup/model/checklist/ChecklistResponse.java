package com.stepup.model.checklist;

import lombok.Builder;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class ChecklistResponse {
    public static final String CHECK_LIST = "{\n" +
            "  \"test_checklist\": {\n" +
            "    \"title\": \"Chức năng Tạo Bài Viết\",\n" +
            "    \"total_checks\": 54,\n" +
            "    \"categories\": [\n" +
            "      {\n" +
            "        \"id\": \"1\",\n" +
            "        \"name\": \"Giao diện người dùng\",\n" +
            "        \"total_checks\": 8,\n" +
            "        \"subcategories\": [\n" +
            "          {\n" +
            "            \"id\": \"1.1\",\n" +
            "            \"name\": \"Bố cục và Hiển thị\",\n" +
            "            \"total_checks\": 5,\n" +
            "            \"tests\": [\n" +
            "              {\n" +
            "                \"id\": 1,\n" +
            "                \"name\": \"Kiểm tra màn hình tạo bài viết hiển thị đúng bố cục, không bị chồng chéo các thành phần UI\",\n" +
            "                \"priority\": \"high\",\n" +
            "                \"status\": \"failed\"\n" +
            "              },\n" +
            "              {\n" +
            "                \"id\": 2,\n" +
            "                \"name\": \"Kiểm tra các thành phần UI như input field (Tiêu đề, Nội dung), nút (Đăng bài, Lưu nháp, Thêm ảnh/video), label, checkbox/radio button (nếu có) hiển thị đầy đủ và đúng vị trí\",\n" +
            "                \"priority\": \"medium\",\n" +
            "                \"status\": \"pending\"\n" +
            "              },\n" +
            "              {\n" +
            "                \"id\": 3,\n" +
            "                \"name\": \"Kiểm tra hiển thị của trình soạn thảo nội dung (WYSIWYG editor) nếu có, bao gồm các tùy chọn định dạng (bold, italic, list, v.v.)\",\n" +
            "                \"priority\": \"low\",\n" +
            "                \"status\": \"passed\"\n" +
            "              },\n" +
            "              {\n" +
            "                \"id\": 4,\n" +
            "                \"name\": \"Kiểm tra các thông báo lỗi/thành công hiển thị rõ ràng, dễ đọc\",\n" +
            "                \"priority\": \"low\",\n" +
            "                \"status\": \"passed\"\n" +
            "              },\n" +
            "              {\n" +
            "                \"id\": 5,\n" +
            "                \"name\": \"Kiểm tra giao diện người dùng tuân thủ bộ nhận diện thương hiệu của dự án (màu sắc, font chữ, icon)\",\n" +
            "                \"priority\": \"medium\",\n" +
            "                \"status\": \"in_progress\"\n" +
            "              }\n" +
            "            ]\n" +
            "          },\n" +
            "          {\n" +
            "            \"id\": \"1.2\",\n" +
            "            \"name\": \"Tương thích thiết bị\",\n" +
            "            \"total_checks\": 3,\n" +
            "            \"tests\": [\n" +
            "              {\n" +
            "                \"id\": 6,\n" +
            "                \"name\": \"Kiểm tra giao diện màn hình tạo bài viết hiển thị tốt trên các kích thước màn hình khác nhau (responsive design)\",\n" +
            "                \"priority\": \"medium\",\n" +
            "                \"status\": \"pending\"\n" +
            "              },\n" +
            "              {\n" +
            "                \"id\": 7,\n" +
            "                \"name\": \"Kiểm tra chức năng nhập liệu và các thao tác khác hoạt động mượt mà trên các thiết bị di động và máy tính bảng\",\n" +
            "                \"priority\": \"medium\",\n" +
            "                \"status\": \"pending\"\n" +
            "              },\n" +
            "              {\n" +
            "                \"id\": 8,\n" +
            "                \"name\": \"Kiểm tra hiển thị và chức năng trên các trình duyệt phổ biến (Chrome, Firefox, Edge, Safari)\",\n" +
            "                \"priority\": \"low\",\n" +
            "                \"status\": \"passed\"\n" +
            "              }\n" +
            "            ]\n" +
            "          }\n" +
            "        ]\n" +
            "      },\n" +
            "      {\n" +
            "        \"id\": \"2\",\n" +
            "        \"name\": \"Chức năng Tạo bài viết\",\n" +
            "        \"total_checks\": 30,\n" +
            "        \"subcategories\": [\n" +
            "          {\n" +
            "            \"id\": \"2.1\",\n" +
            "            \"name\": \"Truy cập màn hình tạo bài\",\n" +
            "            \"total_checks\": 2,\n" +
            "            \"tests\": [\n" +
            "              {\n" +
            "                \"id\": 9,\n" +
            "                \"name\": \"Kiểm tra người dùng có quyền (đã đăng nhập, có vai trò phù hợp) có thể truy cập màn hình tạo bài viết từ điểm truy cập\",\n" +
            "                \"priority\": \"high\",\n" +
            "                \"status\": \"failed\"\n" +
            "              },\n" +
            "              {\n" +
            "                \"id\": 10,\n" +
            "                \"name\": \"Kiểm tra người dùng không có quyền truy cập màn hình tạo bài viết bị chuyển hướng hoặc hiển thị thông báo lỗi phù hợp\",\n" +
            "                \"priority\": \"medium\",\n" +
            "                \"status\": \"pending\"\n" +
            "              }\n" +
            "            ]\n" +
            "          },\n" +
            "          {\n" +
            "            \"id\": \"2.2\",\n" +
            "            \"name\": \"Nhập liệu tiêu đề và nội dung\",\n" +
            "            \"total_checks\": 8,\n" +
            "            \"tests\": [\n" +
            "              {\n" +
            "                \"id\": 11,\n" +
            "                \"name\": \"Kiểm tra có thể nhập Tiêu đề bài viết\",\n" +
            "                \"priority\": \"high\",\n" +
            "                \"status\": \"failed\"\n" +
            "              },\n" +
            "              {\n" +
            "                \"id\": 12,\n" +
            "                \"name\": \"Kiểm tra có thể nhập Nội dung bài viết\",\n" +
            "                \"priority\": \"high\",\n" +
            "                \"status\": \"failed\"\n" +
            "              }\n" +
            "            ]\n" +
            "          }\n" +
            "        ]\n" +
            "      }\n" +
            "    ],\n" +
            "    \"status_summary\": {\n" +
            "      \"passed\": 8,\n" +
            "      \"failed\": 18,\n" +
            "      \"pending\": 23,\n" +
            "      \"in_progress\": 5,\n" +
            "      \"total\": 54\n" +
            "    },\n" +
            "    \"priority_summary\": {\n" +
            "      \"high\": 15,\n" +
            "      \"medium\": 25,\n" +
            "      \"low\": 14\n" +
            "    }\n" +
            "  }\n" +
            "}";

    public static final String TEST_CASE_FAKE = "{\n" +
            "  \"categories\": [\n" +
            "    {\n" +
            "      \"id\": \"1\",\n" +
            "      \"name\": \"Giao diện người dùng\",\n" +
            "      \"total_checks\": 8,\n" +
            "      \"subcategories\": [\n" +
            "        {\n" +
            "          \"id\": \"1.1\",\n" +
            "          \"name\": \"Bố cục và Hiển thị\",\n" +
            "          \"total_checks\": 5,\n" +
            "          \"tests\": [\n" +
            "            {\n" +
            "              \"id\": 1,\n" +
            "              \"name\": \"Kiểm tra màn hình tạo bài viết hiển thị đúng bố cục, không bị chồng chéo các thành phần UI\",\n" +
            "              \"name_unique\": \"TC01: Kiểm tra hiển thị bố cục màn hình tạo bài viết.\",\n" +
            "              \"description\": \"Xác minh rằng màn hình tạo bài viết hiển thị đúng bố cục thiết kế, các thành phần UI được sắp xếp hợp lý và không bị chồng chéo lên nhau.\",\n" +
            "              \"precondition\": \"Người dùng đã đăng nhập thành công và truy cập vào trang tạo bài viết.\",\n" +
            "              \"priority\": \"high\",\n" +
            "              \"status\": \"failed\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Truy cập vào trang tạo bài viết.\",\n" +
            "                  \"expected\": \"Màn hình tạo bài viết hiển thị. Các thành phần UI như khu vực nhập Tiêu đề, khu vực nhập Nội dung, các nút 'Đăng bài', 'Lưu nháp', 'Thêm ảnh/video' và các tùy chọn khác (nếu có) được hiển thị rõ ràng, đúng vị trí và không bị chồng chéo.\",\n" +
            "                  \"data\": \"Nội dung được nhập và định dạng trên thiết bị cảm ứng.\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"id\": 2,\n" +
            "              \"name\": \"Kiểm tra các thành phần UI như input field (Tiêu đề, Nội dung), nút (Đăng bài, Lưu nháp, Thêm ảnh/video), label, checkbox/radio button (nếu có) hiển thị đầy đủ và đúng vị trí\",\n" +
            "              \"name_unique\": \"TC02: Kiểm tra hiển thị đầy đủ các thành phần UI\",\n" +
            "              \"description\": \"Xác minh rằng tất cả các thành phần UI cần thiết được hiển thị đầy đủ và đúng vị trí theo thiết kế.\",\n" +
            "              \"precondition\": \"Người dùng đã đăng nhập thành công và truy cập vào trang tạo bài viết.\",\n" +
            "              \"priority\": \"medium\",\n" +
            "              \"status\": \"pending\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Kiểm tra sự hiện diện của trường nhập Tiêu đề\",\n" +
            "                  \"expected\": \"Trường nhập Tiêu đề hiển thị đúng vị trí với placeholder và label phù hợp\",\n" +
            "                  \"data\": \"N/A\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Kiểm tra sự hiện diện của trường nhập Nội dung\",\n" +
            "                  \"expected\": \"Trường nhập Nội dung hiển thị đúng vị trí với công cụ soạn thảo\",\n" +
            "                  \"data\": \"N/A\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 3,\n" +
            "                  \"action\": \"Kiểm tra các nút chức năng\",\n" +
            "                  \"expected\": \"Các nút 'Đăng bài', 'Lưu nháp', 'Thêm ảnh/video' hiển thị đúng vị trí và có style phù hợp\",\n" +
            "                  \"data\": \"N/A\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"id\": 3,\n" +
            "              \"name\": \"Kiểm tra hiển thị của trình soạn thảo nội dung (WYSIWYG editor) nếu có, bao gồm các tùy chọn định dạng (bold, italic, list, v.v.)\",\n" +
            "              \"name_unique\": \"TC03: Kiểm tra trình soạn thảo WYSIWYG\",\n" +
            "              \"description\": \"Xác minh rằng trình soạn thảo WYSIWYG hoạt động đúng với đầy đủ các tùy chọn định dạng văn bản.\",\n" +
            "              \"precondition\": \"Người dùng đã đăng nhập và truy cập trang tạo bài viết.\",\n" +
            "              \"priority\": \"low\",\n" +
            "              \"status\": \"passed\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Kiểm tra thanh công cụ định dạng\",\n" +
            "                  \"expected\": \"Hiển thị đầy đủ các nút định dạng: Bold, Italic, Underline, List, etc.\",\n" +
            "                  \"data\": \"N/A\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Thử nghiệm các tùy chọn định dạng\",\n" +
            "                  \"expected\": \"Mỗi tùy chọn định dạng hoạt động chính xác khi được click\",\n" +
            "                  \"data\": \"Văn bản mẫu để test định dạng\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"id\": 4,\n" +
            "              \"name\": \"Kiểm tra các thông báo lỗi/thành công hiển thị rõ ràng, dễ đọc\",\n" +
            "              \"name_unique\": \"TC04: Kiểm tra hiển thị thông báo\",\n" +
            "              \"description\": \"Xác minh rằng các thông báo lỗi và thành công hiển thị rõ ràng, dễ đọc và đúng vị trí.\",\n" +
            "              \"precondition\": \"Người dùng đang ở trang tạo bài viết.\",\n" +
            "              \"priority\": \"low\",\n" +
            "              \"status\": \"passed\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Tạo điều kiện hiển thị thông báo lỗi\",\n" +
            "                  \"expected\": \"Thông báo lỗi hiển thị rõ ràng với màu sắc và icon phù hợp\",\n" +
            "                  \"data\": \"Để trống các trường bắt buộc\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Tạo điều kiện hiển thị thông báo thành công\",\n" +
            "                  \"expected\": \"Thông báo thành công hiển thị rõ ràng với màu sắc và icon phù hợp\",\n" +
            "                  \"data\": \"Hoàn thành tạo bài viết hợp lệ\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"id\": 5,\n" +
            "              \"name\": \"Kiểm tra giao diện người dùng tuân thủ bộ nhận diện thương hiệu của dự án (màu sắc, font chữ, icon)\",\n" +
            "              \"name_unique\": \"TC05: Kiểm tra tuân thủ nhận diện thương hiệu\",\n" +
            "              \"description\": \"Xác minh rằng giao diện tuân thủ đúng bộ nhận diện thương hiệu đã định nghĩa.\",\n" +
            "              \"precondition\": \"Có tài liệu về bộ nhận diện thương hiệu của dự án.\",\n" +
            "              \"priority\": \"medium\",\n" +
            "              \"status\": \"in_progress\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Kiểm tra màu sắc sử dụng\",\n" +
            "                  \"expected\": \"Các màu sắc sử dụng đúng với bảng màu của thương hiệu\",\n" +
            "                  \"data\": \"Bảng màu thương hiệu\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Kiểm tra font chữ\",\n" +
            "                  \"expected\": \"Font chữ sử dụng đúng với quy định của thương hiệu\",\n" +
            "                  \"data\": \"Danh sách font được phép sử dụng\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 3,\n" +
            "                  \"action\": \"Kiểm tra icon\",\n" +
            "                  \"expected\": \"Các icon sử dụng đúng với bộ icon của thương hiệu\",\n" +
            "                  \"data\": \"Bộ icon chuẩn của thương hiệu\"\n" +
            "                }\n" +
            "              ]\n" +
            "            }\n" +
            "          ]\n" +
            "        },\n" +
            "        {\n" +
            "          \"id\": \"1.2\",\n" +
            "          \"name\": \"Tương thích thiết bị\",\n" +
            "          \"total_checks\": 3,\n" +
            "          \"tests\": [\n" +
            "            {\n" +
            "              \"id\": 6,\n" +
            "              \"name\": \"Kiểm tra giao diện màn hình tạo bài viết hiển thị tốt trên các kích thước màn hình khác nhau (responsive design)\",\n" +
            "              \"name_unique\": \"TC06: Kiểm tra responsive design\",\n" +
            "              \"description\": \"Xác minh rằng giao diện hiển thị phù hợp trên các kích thước màn hình khác nhau.\",\n" +
            "              \"precondition\": \"Có các thiết bị test hoặc công cụ giả lập với các kích thước màn hình khác nhau.\",\n" +
            "              \"priority\": \"medium\",\n" +
            "              \"status\": \"pending\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Test trên màn hình desktop\",\n" +
            "                  \"expected\": \"Giao diện hiển thị đầy đủ và cân đối\",\n" +
            "                  \"data\": \"Độ phân giải >= 1024px\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Test trên màn hình tablet\",\n" +
            "                  \"expected\": \"Giao diện tự động điều chỉnh phù hợp\",\n" +
            "                  \"data\": \"Độ phân giải 768px - 1023px\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 3,\n" +
            "                  \"action\": \"Test trên màn hình mobile\",\n" +
            "                  \"expected\": \"Giao diện tự động điều chỉnh phù hợp với màn hình nhỏ\",\n" +
            "                  \"data\": \"Độ phân giải < 768px\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"id\": 7,\n" +
            "              \"name\": \"Kiểm tra chức năng nhập liệu và các thao tác khác hoạt động mượt mà trên các thiết bị di động và máy tính bảng\",\n" +
            "              \"name_unique\": \"TC07: Kiểm tra tương tác trên thiết bị di động\",\n" +
            "              \"description\": \"Xác minh rằng các chức năng nhập liệu và thao tác hoạt động tốt trên thiết bị di động.\",\n" +
            "              \"precondition\": \"Có sẵn các thiết bị di động và máy tính bảng để test.\",\n" +
            "              \"priority\": \"medium\",\n" +
            "              \"status\": \"pending\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Test nhập liệu trên thiết bị cảm ứng\",\n" +
            "                  \"expected\": \"Bàn phím ảo hiển thị đúng, không che khuất nội dung quan trọng\",\n" +
            "                  \"data\": \"Sử dụng iPhone và iPad\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Test các thao tác touch\",\n" +
            "                  \"expected\": \"Các nút bấm có kích thước phù hợp để thao tác bằng touch\",\n" +
            "                  \"data\": \"Sử dụng các thiết bị Android khác nhau\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"id\": 8,\n" +
            "              \"name\": \"Kiểm tra hiển thị và chức năng trên các trình duyệt phổ biến (Chrome, Firefox, Edge, Safari)\",\n" +
            "              \"name_unique\": \"TC08: Kiểm tra tương thích trình duyệt\",\n" +
            "              \"description\": \"Xác minh rằng ứng dụng hoạt động nhất quán trên các trình duyệt phổ biến.\",\n" +
            "              \"precondition\": \"Có cài đặt các trình duyệt cần test.\",\n" +
            "              \"priority\": \"low\",\n" +
            "              \"status\": \"passed\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Test trên Google Chrome\",\n" +
            "                  \"expected\": \"Tất cả chức năng hoạt động bình thường\",\n" +
            "                  \"data\": \"Chrome phiên bản mới nhất\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Test trên Firefox\",\n" +
            "                  \"expected\": \"Tất cả chức năng hoạt động bình thường\",\n" +
            "                  \"data\": \"Firefox phiên bản mới nhất\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 3,\n" +
            "                  \"action\": \"Test trên Edge\",\n" +
            "                  \"expected\": \"Tất cả chức năng hoạt động bình thường\",\n" +
            "                  \"data\": \"Edge phiên bản mới nhất\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 4,\n" +
            "                  \"action\": \"Test trên Safari\",\n" +
            "                  \"expected\": \"Tất cả chức năng hoạt động bình thường\",\n" +
            "                  \"data\": \"Safari phiên bản mới nhất\"\n" +
            "                }\n" +
            "              ]\n" +
            "            }\n" +
            "          ]\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"id\": \"2\",\n" +
            "      \"name\": \"Chức năng Tạo bài viết\",\n" +
            "      \"total_checks\": 30,\n" +
            "      \"subcategories\": [\n" +
            "        {\n" +
            "          \"id\": \"2.1\",\n" +
            "          \"name\": \"Truy cập màn hình tạo bài\",\n" +
            "          \"total_checks\": 2,\n" +
            "          \"tests\": [\n" +
            "            {\n" +
            "              \"id\": 9,\n" +
            "              \"name\": \"Kiểm tra người dùng có quyền (đã đăng nhập, có vai trò phù hợp) có thể truy cập màn hình tạo bài viết từ điểm truy cập\",\n" +
            "              \"name_unique\": \"TC09: Kiểm tra quyền truy cập cho người dùng hợp lệ\",\n" +
            "              \"description\": \"Xác minh rằng người dùng có quyền có thể truy cập màn hình tạo bài viết.\",\n" +
            "              \"precondition\": \"Có tài khoản người dùng với các vai trò khác nhau.\",\n" +
            "              \"priority\": \"high\",\n" +
            "              \"status\": \"failed\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Đăng nhập với tài khoản có quyền tạo bài\",\n" +
            "                  \"expected\": \"Đăng nhập thành công\",\n" +
            "                  \"data\": \"Tài khoản admin\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Truy cập vào trang tạo bài viết\",\n" +
            "                  \"expected\": \"Truy cập thành công, hiển thị form tạo bài\",\n" +
            "                  \"data\": \"URL trang tạo bài\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"id\": 10,\n" +
            "              \"name\": \"Kiểm tra người dùng không có quyền truy cập màn hình tạo bài viết bị chuyển hướng hoặc hiển thị thông báo lỗi phù hợp\",\n" +
            "              \"name_unique\": \"TC10: Kiểm tra hạn chế truy cập cho người dùng không có quyền\",\n" +
            "              \"description\": \"Xác minh rằng người dùng không có quyền không thể truy cập màn hình tạo bài viết.\",\n" +
            "              \"precondition\": \"Có tài khoản người dùng không có quyền tạo bài.\",\n" +
            "              \"priority\": \"medium\",\n" +
            "              \"status\": \"pending\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Đăng nhập với tài khoản không có quyền tạo bài\",\n" +
            "                  \"expected\": \"Đăng nhập thành công\",\n" +
            "                  \"data\": \"Tài khoản user thường\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Cố gắng truy cập vào trang tạo bài viết\",\n" +
            "                  \"expected\": \"Hiển thị thông báo lỗi hoặc chuyển hướng về trang chủ\",\n" +
            "                  \"data\": \"URL trang tạo bài\"\n" +
            "                }\n" +
            "              ]\n" +
            "            }\n" +
            "          ]\n" +
            "        },\n" +
            "        {\n" +
            "          \"id\": \"2.2\",\n" +
            "          \"name\": \"Nhập liệu tiêu đề và nội dung\",\n" +
            "          \"total_checks\": 8,\n" +
            "          \"tests\": [\n" +
            "            {\n" +
            "              \"id\": 11,\n" +
            "              \"name\": \"Kiểm tra có thể nhập Tiêu đề bài viết\",\n" +
            "              \"name_unique\": \"TC11: Kiểm tra nhập tiêu đề bài viết\",\n" +
            "              \"description\": \"Xác minh rằng người dùng có thể nhập và chỉnh sửa tiêu đề bài viết.\",\n" +
            "              \"precondition\": \"Người dùng đã đăng nhập và truy cập trang tạo bài viết.\",\n" +
            "              \"priority\": \"high\",\n" +
            "              \"status\": \"failed\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Nhập tiêu đề bài viết\",\n" +
            "                  \"expected\": \"Tiêu đề được nhập và hiển thị đúng\",\n" +
            "                  \"data\": \"Tiêu đề test: Bài viết mới\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Kiểm tra giới hạn ký tự tiêu đề\",\n" +
            "                  \"expected\": \"Hiển thị cảnh báo khi vượt quá giới hạn\",\n" +
            "                  \"data\": \"Tiêu đề dài hơn giới hạn cho phép\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"id\": 12,\n" +
            "              \"name\": \"Kiểm tra có thể nhập Nội dung bài viết\",\n" +
            "              \"name_unique\": \"TC12: Kiểm tra nhập nội dung bài viết\",\n" +
            "              \"description\": \"Xác minh rằng người dùng có thể nhập và định dạng nội dung bài viết.\",\n" +
            "              \"precondition\": \"Người dùng đã đăng nhập và truy cập trang tạo bài viết.\",\n" +
            "              \"priority\": \"high\",\n" +
            "              \"status\": \"failed\",\n" +
            "              \"steps\": [\n" +
            "                {\n" +
            "                  \"id\": 1,\n" +
            "                  \"action\": \"Nhập nội dung bài viết\",\n" +
            "                  \"expected\": \"Nội dung được nhập và hiển thị đúng\",\n" +
            "                  \"data\": \"Nội dung test với các định dạng khác nhau\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"id\": 2,\n" +
            "                  \"action\": \"Sử dụng các công cụ định dạng\",\n" +
            "                  \"expected\": \"Các công cụ định dạng hoạt động chính xác\",\n" +
            "                  \"data\": \"Áp dụng bold, italic, list, etc.\"\n" +
            "                }\n" +
            "              ]\n" +
            "            }\n" +
            "          ]\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}";
}