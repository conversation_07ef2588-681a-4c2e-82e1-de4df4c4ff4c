package com.stepup.controller;

import com.stepup.dto.DataResponseDTO;
import com.stepup.dto.GoogleLoginRequest;
import com.stepup.dto.GoogleLoginResponse;
import com.stepup.dto.GenerateJwtRequest;
import com.stepup.dto.GenerateJwtDevRequest;
import com.stepup.model.User;
import com.stepup.service.JwtService;
import com.stepup.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtService jwtService;

    @PostMapping("/google/login")
    public ResponseEntity<DataResponseDTO<GoogleLoginResponse>> googleLogin(@RequestBody GoogleLoginRequest request) {
        try {
            System.out.println("🔍 Processing Google login for email: " + request.getEmail());

            // Find or create user using existing UserService
            User user = userService.findOrCreateUser(
                    request.getEmail(),
                    request.getName(),
                    request.getGoogleId(),
                    request.getProfilePicture());

            // Generate JWT token
            String jwt = jwtService.generateTokenForUser(user.getEmail(), user.getId(), user.getRole().name());

            GoogleLoginResponse response = new GoogleLoginResponse();
            response.setJwt(jwt);
            response.setUserId(user.getId());
            response.setEmail(user.getEmail());
            response.setName(user.getName());
            response.setProfilePicture(user.getProfilePicture());

            System.out.println("✅ Google login successful for user: " + user.getEmail());
            return ResponseEntity.ok(DataResponseDTO.success(response, "Login successful"));

        } catch (Exception e) {
            System.err.println("❌ Google login failed: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Login failed: " + e.getMessage(), "LOGIN_FAILED"));
        }
    }

    @GetMapping("/profile")
    public ResponseEntity<DataResponseDTO<Map<String, Object>>> getUserProfile(
            @RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.replace("Bearer ", "");
            String email = jwtService.extractUsername(token);

            if (jwtService.validateToken(token, email)) {
                Optional<User> userOpt = userService.findByEmail(email);
                if (userOpt.isPresent()) {
                    User user = userOpt.get();
                    Map<String, Object> userProfile = Map.of(
                            "id", user.getId(),
                            "email", user.getEmail(),
                            "name", user.getName(),
                            "profilePicture", user.getProfilePicture(),
                            "provider", user.getProvider(),
                            "role", user.getRole(),
                            "createdAt", user.getCreatedAt());

                    return ResponseEntity.ok(DataResponseDTO.success(userProfile, "Profile retrieved successfully"));
                }
            }
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(DataResponseDTO.error("Invalid token", "INVALID_TOKEN"));
        } catch (Exception e) {
            System.err.println("❌ Profile fetch failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to fetch profile", "PROFILE_FETCH_FAILED"));
        }
    }

    @GetMapping("/user/{email}")
    public ResponseEntity<DataResponseDTO<Map<String, Object>>> getUserByEmail(@PathVariable String email) {
        Optional<User> userOpt = userService.findByEmail(email);

        if (userOpt.isPresent()) {
            User user = userOpt.get();
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("email", user.getEmail());
            userInfo.put("name", user.getName());
            userInfo.put("profilePicture", user.getProfilePicture());
            userInfo.put("role", user.getRole());

            return ResponseEntity.ok(DataResponseDTO.success(userInfo, "User found"));
        }

        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(DataResponseDTO.error("User not found with email: " + email, "USER_NOT_FOUND"));
    }

    @PostMapping("/logout")
    public ResponseEntity<DataResponseDTO<String>> logout() {
        return ResponseEntity.ok(DataResponseDTO.success("Logged out successfully", "User logged out successfully"));
    }

    @GetMapping("/status")
    public ResponseEntity<DataResponseDTO<String>> getAuthStatus() {
        return ResponseEntity.ok(DataResponseDTO.success("Auth service is running", "Service health check successful"));
    }

    @PostMapping("/generate-jwt")
    public ResponseEntity<DataResponseDTO<Map<String, Object>>> generateJwt(@RequestBody GenerateJwtRequest request) {
        try {
            // Validate request
            if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(DataResponseDTO.error("Email is required", "INVALID_REQUEST"));
            }
            
            // Find user by email
            Optional<User> userOpt = userService.findByEmail(request.getEmail());
            
            if (userOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(DataResponseDTO.error("User not found with email: " + request.getEmail(), "USER_NOT_FOUND"));
            }
            
            User user = userOpt.get();
            
            // Generate JWT token
            String jwt = jwtService.generateTokenForUser(user.getEmail(), user.getId(), user.getRole().name());
            
            // Prepare response
            Map<String, Object> response = new HashMap<>();
            response.put("jwt", jwt);
            response.put("email", user.getEmail());
            response.put("userId", user.getId());
            response.put("role", user.getRole().name());
            response.put("expiresIn", "24 hours");
            
            System.out.println("✅ JWT generated successfully for user: " + user.getEmail());
            return ResponseEntity.ok(DataResponseDTO.success(response, "JWT token generated successfully"));
            
        } catch (Exception e) {
            System.err.println("❌ JWT generation failed: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("JWT generation failed: " + e.getMessage(), "JWT_GENERATION_FAILED"));
        }
    }
    
    @PostMapping("/generate-jwt-dev")
    public ResponseEntity<DataResponseDTO<Map<String, Object>>> generateJwtForDev(@RequestBody GenerateJwtDevRequest request) {
        try {
            // This endpoint is for development/testing only
            // Validate request
            if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(DataResponseDTO.error("Email is required", "INVALID_REQUEST"));
            }
            
            // Use default values if not provided
            String email = request.getEmail();
            String role = (request.getRole() != null && !request.getRole().trim().isEmpty()) ? 
                         request.getRole().toUpperCase() : "USER";
            String userId = (request.getUserId() != null) ? request.getUserId() : "999"; // Default test user ID
            
            // Generate JWT token with provided or default values
            String jwt = jwtService.generateTokenForUser(email, userId, role);
            
            // Prepare response
            Map<String, Object> response = new HashMap<>();
            response.put("jwt", jwt);
            response.put("email", email);
            response.put("userId", userId);
            response.put("role", role);
            response.put("expiresIn", "24 hours");
            response.put("note", "This is for development/testing only");
            
            System.out.println("✅ Development JWT generated for: " + email + " with role: " + role);
            return ResponseEntity.ok(DataResponseDTO.success(response, "Development JWT token generated successfully"));
            
        } catch (Exception e) {
            System.err.println("❌ Development JWT generation failed: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("JWT generation failed: " + e.getMessage(), "JWT_GENERATION_FAILED"));
        }
    }
    
    // Helper method to extract values from User toString()
    private String extractFromUserString(String userStr, String key) {
        try {
            int startIndex = userStr.indexOf(key) + key.length();
            int endIndex = userStr.indexOf(",", startIndex);
            if (endIndex == -1) {
                endIndex = userStr.indexOf(")", startIndex);
            }
            return userStr.substring(startIndex, endIndex).trim();
        } catch (Exception e) {
            return "unknown";
        }
    }
}