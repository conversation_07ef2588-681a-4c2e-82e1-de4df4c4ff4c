package com.stepup.controller;

import com.stepup.dto.DataResponseDTO;
import com.stepup.model.User;
import com.stepup.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class TestController {

    @Autowired
    private CommonService commonService;

    @GetMapping("/current-user")
    public ResponseEntity<DataResponseDTO<Map<String, Object>>> getCurrentUser() {
        try {
            // Sử dụng CommonService để lấy user từ request
            User currentUser = commonService.retrieveUserFromRequest();
            
            if (currentUser == null) {
                return ResponseEntity.ok(DataResponseDTO.error("No authenticated user found", "USER_NOT_FOUND"));
            }
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("email", commonService.getCurrentUserEmail());
            userInfo.put("userId", commonService.getCurrentUserId());
            userInfo.put("isAuthenticated", commonService.isUserAuthenticated());
            userInfo.put("isAdmin", commonService.hasRole("ADMIN"));
            userInfo.put("isUser", commonService.hasRole("USER"));
            userInfo.put("userObject", currentUser.toString()); // Show user object as string để avoid getter issues
            
            return ResponseEntity.ok(DataResponseDTO.success(userInfo, "User information retrieved successfully"));
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(DataResponseDTO.error("Failed to get current user: " + e.getMessage(), "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/auth-status")
    public ResponseEntity<DataResponseDTO<Map<String, Object>>> getAuthStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("isAuthenticated", commonService.isUserAuthenticated());
        status.put("currentUserEmail", commonService.getCurrentUserEmail());
        status.put("currentUserId", commonService.getCurrentUserId());
        
        return ResponseEntity.ok(DataResponseDTO.success(status, "Authentication status retrieved"));
    }
} 