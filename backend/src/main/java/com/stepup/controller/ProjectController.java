package com.stepup.controller;

import com.stepup.dto.DataResponseDTO;
import com.stepup.dto.project.CreateProjectReqDTO;
import com.stepup.dto.project.ProjectResDTO;
import com.stepup.service.ProjectService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/projects")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class ProjectController {
    @Autowired
    private ProjectService projectService;

    @PostMapping
    public ResponseEntity<DataResponseDTO<ProjectResDTO>> createProject(
            @Valid @RequestBody CreateProjectReqDTO request) {

        ProjectResDTO response = projectService.createProject(request);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(DataResponseDTO.success(response, "Project created successfully"));
    }

    @GetMapping
    public ResponseEntity<DataResponseDTO<List<ProjectResDTO>>> getAllProjects(
            @RequestParam(required = false) String createdBy) {
        try {
            List<ProjectResDTO> projects;
            String message;

            if (createdBy != null) {
                projects = projectService.getProjectsByCreatedBy(createdBy);
                message = "Retrieved " + projects.size() + " projects for user: " + createdBy;
            } else {
                projects = projectService.getAllProjects();
                message = "Retrieved all " + projects.size() + " projects";
            }

            return ResponseEntity.ok(DataResponseDTO.success(projects, message));

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to get projects: " + e.getMessage(), "PROJECTS_FETCH_FAILED"));
        }
    }

    @PutMapping("/{project_id}")
    public ResponseEntity<DataResponseDTO<ProjectResDTO>> updateProject(@PathVariable("project_id") String id,
                                                                        @Valid @RequestBody CreateProjectReqDTO request) {
        try {
            ProjectResDTO response = projectService.updateProject(id, request);

            return ResponseEntity.ok(DataResponseDTO.success(response, "Project updated successfully"));

        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(DataResponseDTO.error(e.getMessage(), "PROJECT_NOT_FOUND"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to update project: " + e.getMessage(),
                            "PROJECT_UPDATE_FAILED"));
        }
    }

    @DeleteMapping("/{project_id}")
    public ResponseEntity<DataResponseDTO<String>> deleteProject(@PathVariable("project_id") String id) {
        projectService.deleteProject(id);

        return ResponseEntity.ok(DataResponseDTO.success("Project deleted successfully",
                "Project with ID " + id + " has been deleted"));
    }

    @GetMapping("/status")
    public ResponseEntity<DataResponseDTO<String>> getStatus() {
        return ResponseEntity
                .ok(DataResponseDTO.success("Project service is running", "Service health check successful"));
    }

    @PostMapping("/{project_id}/features")
    public ResponseEntity<DataResponseDTO<?>> createFeature(@PathVariable("project_id") String projectId) {
        return ResponseEntity
                .ok(DataResponseDTO.success(projectService.createFeature(projectId), "create feature successful!"));
    }
}