package com.stepup.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.dto.DataResponseDTO;
import com.stepup.service.ChecklistService;
import com.stepup.service.OpenAIService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@RestController
@RequestMapping("/api/openai")
public class OpenAIController {

    private static final Logger log = LoggerFactory.getLogger(OpenAIController.class);
    private final OpenAIService openAIService;
    private final ChecklistService checklistService;
    private static final String UPLOAD_DIR = "uploads";

    @Autowired
    public OpenAIController(OpenAIService openAIService, ChecklistService checklistService) {
        this.openAIService = openAIService;
        this.checklistService = checklistService;
        // Create upload directory if it doesn't exist
        try {
            Files.createDirectories(Path.of(UPLOAD_DIR));
        } catch (IOException e) {
            log.error("Failed to create upload directory", e);
        }
    }

    @PostMapping("/chat-with-file")
    public ResponseEntity<DataResponseDTO<?>> chatWithFile(
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam("text") String text,
            @RequestParam("feature_id") String featureId) {
        try {
            JsonNode response = checklistService.generateChecklist(file, text, featureId);
            return ResponseEntity.ok(DataResponseDTO.success(response, "Checklist generated successfully"));
        } catch (IOException e) {
            log.error("Error processing file upload", e);
            return ResponseEntity.badRequest()
                    .body(DataResponseDTO.error("Error processing file: " + e.getMessage(), "FILE_PROCESSING_ERROR"));
        } catch (Exception e) {
            log.error("Error generating checklist", e);
            return ResponseEntity.internalServerError()
                    .body(DataResponseDTO.error("Error: " + e.getMessage(), "CHECKLIST_GENERATION_FAILED"));
        }
    }

    @PostMapping("/gen-testcase")
    public ResponseEntity<DataResponseDTO<?>> chatWithFile(
            @RequestParam("text") String text) {
        try {
            JsonNode response = checklistService.generateTestcase(text);
            return ResponseEntity.ok(DataResponseDTO.success(response, "Checklist generated successfully"));
        } catch (IOException e) {
            log.error("Error processing file upload", e);
            return ResponseEntity.badRequest()
                    .body(DataResponseDTO.error("Error processing file: " + e.getMessage(), "FILE_PROCESSING_ERROR"));
        } catch (Exception e) {
            log.error("Error generating checklist", e);
            return ResponseEntity.internalServerError()
                    .body(DataResponseDTO.error("Error: " + e.getMessage(), "CHECKLIST_GENERATION_FAILED"));
        }
    }

}