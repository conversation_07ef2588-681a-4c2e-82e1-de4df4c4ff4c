package com.stepup.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.dto.DataResponseDTO;
import com.stepup.dto.checklist.GenerateChecklistReqDTO;
import com.stepup.service.ChecklistService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/api/checklists")
@RequiredArgsConstructor
public class ChecklistController {

    private final ChecklistService checklistService;

    @GetMapping
    public ResponseEntity<DataResponseDTO<JsonNode>> getChecklist() {
        try {
            JsonNode checklist = checklistService.getChecklist();
            return ResponseEntity.ok(DataResponseDTO.success(checklist, "Checklist retrieved successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(DataResponseDTO.error("Failed to retrieve checklist: " + e.getMessage(),
                            "CHECKLIST_FETCH_FAILED"));
        }
    }

    @PostMapping("/generate")
    public ResponseEntity<DataResponseDTO<?>> chatWithFile(
            @RequestBody GenerateChecklistReqDTO generateChecklistReqDTO
    ) {
        try {
            JsonNode response = checklistService.generateChecklist(null, generateChecklistReqDTO.getText(), generateChecklistReqDTO.getFeatureId());
            return ResponseEntity.ok(DataResponseDTO.success(response, "Checklist generated successfully"));
        } catch (IOException e) {
            return ResponseEntity.badRequest()
                    .body(DataResponseDTO.error("Error processing file: " + e.getMessage(), "FILE_PROCESSING_ERROR"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(DataResponseDTO.error("Error: " + e.getMessage(), "CHECKLIST_GENERATION_FAILED"));
        }
    }

    @GetMapping("/{feature_id}")
    public ResponseEntity<DataResponseDTO<?>> getChecklistByFeatureId(
            @RequestBody GenerateChecklistReqDTO generateChecklistReqDTO
    ) {
        return null;
    }
}