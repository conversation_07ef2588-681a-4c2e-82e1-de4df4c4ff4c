package com.stepup.service;

import com.stepup.model.User;
import com.stepup.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;
    
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    public Optional<User> findByGoogleId(String googleId) {
        return userRepository.findByGoogleId(googleId);
    }
    
    public User createUser(String email, String name, String googleId, String profilePicture) {
        User user = User.builder()
                .email(email)
                .name(name)
                .googleId(googleId)
                .profilePicture(profilePicture)
                .provider(User.Provider.GOOGLE)
                .role(User.Role.USER)
                .enabled(true)
                .build();
        
        return userRepository.save(user);
    }
    
    public User updateUser(User user) {
        return userRepository.save(user);
    }
    
    public User findOrCreateUser(String email, String name, String googleId, String profilePicture) {
        Optional<User> existingUser = findByGoogleId(googleId);
        
        if (existingUser.isPresent()) {
            User user = existingUser.get();
            // Update user info if needed
            boolean needsUpdate = false;
            
            if (!user.getName().equals(name)) {
                user.setName(name);
                needsUpdate = true;
            }
            
            if (!user.getEmail().equals(email)) {
                user.setEmail(email);
                needsUpdate = true;
            }
            
            if (profilePicture != null && !profilePicture.equals(user.getProfilePicture())) {
                user.setProfilePicture(profilePicture);
                needsUpdate = true;
            }
            
            return needsUpdate ? updateUser(user) : user;
        } else {
            return createUser(email, name, googleId, profilePicture);
        }
    }
} 