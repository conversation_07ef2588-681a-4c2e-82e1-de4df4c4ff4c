package com.stepup.service;

import com.stepup.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CommonService {

    private static final Logger logger = LoggerFactory.getLogger(CommonService.class);

    @Autowired
    private UserService userService;

    /**
     * Retrieve User object from current request authentication context
     *
     * @return User object if authenticated, null if not authenticated or user not found
     */
    public User retrieveUserFromRequest() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            if (authentication == null || !authentication.isAuthenticated()) {
                logger.warn("No authenticated user found in security context");
                return null;
            }

            // Get username (email) from authentication principal
            String userEmail = authentication.getName();

            if (userEmail == null || userEmail.equals("anonymousUser")) {
                logger.warn("Anonymous user or null email in authentication");
                return null;
            }

            // Find user by email
            Optional<User> userOpt = userService.findByEmail(userEmail);

            if (userOpt.isPresent()) {
                logger.debug("User retrieved successfully: {}", userEmail);
                return userOpt.get();
            } else {
                logger.warn("User not found in database: {}", userEmail);
                return null;
            }

        } catch (Exception e) {
            logger.error("Error retrieving user from request: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Get current authenticated user's email
     *
     * @return User email if authenticated, null otherwise
     */
    public String getCurrentUserEmail() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            if (authentication != null && authentication.isAuthenticated()) {
                String email = authentication.getName();
                return (email != null && !email.equals("anonymousUser")) ? email : null;
            }

            return null;
        } catch (Exception e) {
            logger.error("Error getting current user email: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Check if current user is authenticated
     *
     * @return true if user is authenticated, false otherwise
     */
    public boolean isUserAuthenticated() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return authentication != null &&
                    authentication.isAuthenticated() &&
                    !authentication.getName().equals("anonymousUser");
        } catch (Exception e) {
            logger.error("Error checking authentication status: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Get current authenticated user's ID
     *
     * @return User ID if authenticated and user exists, null otherwise
     */
    public String getCurrentUserId() {
        User user = retrieveUserFromRequest();
        if (user != null) {
            return user.getId();
        }
        return null;
    }

    /**
     * Check if current user has specific role
     *
     * @param role Role to check (USER, ADMIN)
     * @return true if user has the role, false otherwise
     */
    public boolean hasRole(String role) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            if (authentication == null || !authentication.isAuthenticated()) {
                return false;
            }

            return authentication.getAuthorities().stream()
                    .anyMatch(authority -> authority.getAuthority().equals("ROLE_" + role));

        } catch (Exception e) {
            logger.error("Error checking user role: {}", e.getMessage());
            return false;
        }
    }
} 