package com.stepup.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.mapper.ChecklistParser;
import com.stepup.model.User;
import com.stepup.model.ai.AIPromptBucket;
import com.stepup.model.ai.ModelAIProvider;
import com.stepup.model.checklist.ChecklistResponse;
import com.stepup.model.checklist.ChecklistTemplate;
import com.stepup.repository.AIPromptBucketRepository;
import com.stepup.repository.AIProviderTokensRepository;
import com.stepup.repository.ChecklistTemplateRepository;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.UUID;

@Service
public class ChecklistService extends CommonService {
    private static final Logger log = LoggerFactory.getLogger(ChecklistService.class);
    private final ObjectMapper objectMapper;
    private final OpenAIService openAIService;
    private final ChecklistParser checklistParser;
    private static final String UPLOAD_DIR = "temp_uploads";

    private final AIPromptBucketRepository aiPromptBucketRepository;
    private final AIProviderTokensRepository aiProviderTokensRepository;
    private final ChecklistTemplateRepository checklistTemplateRepository;

    @Autowired
    public ChecklistService(ObjectMapper objectMapper, OpenAIService openAIService,
                            ChecklistParser checklistParser,
                            AIPromptBucketRepository aiPromptBucketRepository,
                            AIProviderTokensRepository aiProviderTokensRepository,
                            ChecklistTemplateRepository checklistTemplateRepository) {
        this.objectMapper = objectMapper;
        this.openAIService = openAIService;
        this.checklistParser = checklistParser;
        this.aiPromptBucketRepository = aiPromptBucketRepository;
        this.aiProviderTokensRepository = aiProviderTokensRepository;
        this.checklistTemplateRepository = checklistTemplateRepository;
    }

    public JsonNode getChecklist() {
        try {
            // Read the JSON file from resources
            ClassPathResource resource = new ClassPathResource("data/checklist-demo.json");
            try (InputStream inputStream = resource.getInputStream()) {
                return objectMapper.readTree(ChecklistResponse.CHECK_LIST);
            }
        } catch (IOException e) {
            log.error("Error reading checklist data", e);
            throw new RuntimeException("Failed to load checklist data", e);
        }
    }

    public JsonNode getChecklistByFeatureId(String featureId) {
        ChecklistTemplate template = checklistTemplateRepository.findByFeatureId(featureId).get();
        try {
            return objectMapper.readTree(template.getData());
        } catch (IOException e) {
            log.error("Error reading checklist data", e);
            throw new RuntimeException("Failed to load checklist data", e);
        }
    }

    public JsonNode generateChecklist(MultipartFile file, String text, String featureId) throws IOException {
        User user = retrieveUserFromRequest();

        ChecklistTemplate template = checklistTemplateRepository.findByFeatureId(featureId).orElseGet(() -> {
            ChecklistTemplate newTemplate = ChecklistTemplate.builder()
                    .createdBy(user.getId())
                    .featureId(featureId)
                    .isActive(true)
                    .build();
                    new ChecklistTemplate();
            return checklistTemplateRepository.save(newTemplate);
        });

        if (template.getData() != null) {
            return objectMapper.readTree(template.getData());
        }

        AIPromptBucket prompt = aiPromptBucketRepository.findByName("gen_checklist_v1").get();
        String apiKey = aiProviderTokensRepository.findByProvider(ModelAIProvider.OPENAI).get().getToken();
        String response;

        if (file == null) {
            response = openAIService.generateResponseWithFile(
                    prompt.getPrompt(),
                    text,
                    null,  // filePath is null
                    apiKey,
                    prompt.getModel(),
                    Integer.parseInt(prompt.getMaxToken()),
                    Double.parseDouble(prompt.getTemperature())
            );
        } else {
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null ?
                    originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
            String uniqueFilename = UUID.randomUUID().toString() + extension;

            // Create upload directory if it doesn't exist
            Path uploadDir = Path.of(UPLOAD_DIR);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // Save file temporarily
            Path tempFilePath = uploadDir.resolve(uniqueFilename);
            try {
                Files.copy(file.getInputStream(), tempFilePath, StandardCopyOption.REPLACE_EXISTING);

                // Call OpenAI service to generate checklist
                response = openAIService.generateResponseWithFile(
                        prompt.getPrompt(),
                        text,
                        tempFilePath.toString(),
                        apiKey,
                        prompt.getModel(),
                        Integer.parseInt(prompt.getMaxToken()),
                        Double.parseDouble(prompt.getTemperature())
                );

            } finally {
                // Clean up temporary file
                Files.deleteIfExists(tempFilePath);
            }
        }

        String parsedJson = "";

        if (prompt.getResponseType().equals("json")) {
            parsedJson = response;
        } else if (prompt.getResponseType().equals("markdown")) {
            // Parse the response text to JSON format
            parsedJson = checklistParser.parseChecklistToJson(response);
        }

        template.setIsActive(true);
        template.setData(parsedJson);
        template.setFeatureId(featureId);
        checklistTemplateRepository.save(template);

        return objectMapper.readTree(parsedJson);
    }

    public JsonNode generateTestcase(String text) throws IOException {
        User user = retrieveUserFromRequest();

        AIPromptBucket prompt = aiPromptBucketRepository.findByName("gen_testcase_v1").get();
        String apiKey = aiProviderTokensRepository.findByProvider(ModelAIProvider.OPENAI).get().getToken();

        List<ChatMessage> messages = List.of(
                new ChatMessage(ChatMessageRole.SYSTEM.value(), prompt.getPrompt().replace("{{res_language}}", prompt.getResponseLanguage())),
                new ChatMessage(ChatMessageRole.USER.value(), text)
        );

        String response;

        response = openAIService.generateResponse(
                prompt.getPrompt(),
                messages,
                apiKey,
                prompt.getModel(),
                Integer.parseInt(prompt.getMaxToken()),
                Double.parseDouble(prompt.getTemperature())
        );

        String parsedJson = "";

        if (prompt.getResponseType().equals("json")) {
            parsedJson = response;
        } else if (prompt.getResponseType().equals("markdown")) {
            // Parse the response text to JSON format
            parsedJson = checklistParser.parseTestCasesToJson(response);
        } else {
            // Default: treat as text and parse to JSON
            parsedJson = checklistParser.parseTestCasesToJson(response);
        }

        return objectMapper.readTree(parsedJson);
    }
} 