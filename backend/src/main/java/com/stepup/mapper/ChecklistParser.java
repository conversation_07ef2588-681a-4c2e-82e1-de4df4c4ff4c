package com.stepup.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class ChecklistParser {

    private final ObjectMapper objectMapper;

    public ChecklistParser(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public String parseChecklistToJson(String content) {
        try {
            ObjectNode root = objectMapper.createObjectNode();
            ObjectNode testChecklist = objectMapper.createObjectNode();
            ArrayNode categories = objectMapper.createArrayNode();

            String[] lines = content.split("\n");

            // Extract title from first line
            String title = "";
            for (String line : lines) {
                if (line.trim().startsWith("F:")) {
                    title = line.trim().substring(2).trim();
                    break;
                }
            }

            ObjectNode currentCategory = null;
            ObjectNode currentSubcategory = null;
            ArrayNode currentTests = null;

            int testIdCounter = 1;
            int categoryCounter = 1;
            int subcategoryCounter = 1;
            int totalChecks = 0;

            Pattern headerPattern = Pattern.compile("^#{1,2}\\s*(.+)$");
            Pattern subHeaderPattern = Pattern.compile("^#{3,4}\\s*(.+)$");
            Pattern testPattern = Pattern.compile("^\\[(\\d+)\\]\\s*(.+?)(?:\\s*\\[(.+?)\\])?$");

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // Main headers (# or ##)
                Matcher headerMatcher = headerPattern.matcher(line);
                if (headerMatcher.matches()) {
                    String headerText = headerMatcher.group(1).trim();
                    if (headerText.startsWith("F:") || headerText.contains("Payment Functionality")) {
                        continue;
                    }

                    // Finish previous category
                    finishCategory(currentCategory, currentSubcategory);

                    // Create new category
                    currentCategory = objectMapper.createObjectNode();
                    currentCategory.put("id", String.valueOf(categoryCounter++));
                    currentCategory.put("name", headerText);
                    currentCategory.put("total_checks", 0);
                    currentCategory.set("subcategories", objectMapper.createArrayNode());
                    categories.add(currentCategory);

                    currentSubcategory = null;
                    subcategoryCounter = 1;
                    continue;
                }

                // Sub headers (### or ####)
                Matcher subHeaderMatcher = subHeaderPattern.matcher(line);
                if (subHeaderMatcher.matches()) {
                    String subHeaderText = subHeaderMatcher.group(1).trim();

                    if (currentCategory != null) {
                        // Finish previous subcategory
                        finishSubcategory(currentSubcategory);

                        // Create new subcategory
                        currentSubcategory = objectMapper.createObjectNode();
                        String subcategoryId = currentCategory.get("id").asText() + "." + subcategoryCounter++;
                        currentSubcategory.put("id", subcategoryId);
                        currentSubcategory.put("name", subHeaderText);
                        currentSubcategory.put("total_checks", 0);
                        currentTests = objectMapper.createArrayNode();
                        currentSubcategory.set("tests", currentTests);

                        ((ArrayNode) currentCategory.get("subcategories")).add(currentSubcategory);
                    }
                    continue;
                }

                // Test cases
                Matcher testMatcher = testPattern.matcher(line);
                if (testMatcher.matches()) {
                    String priority = "[" + testMatcher.group(1) + "]";
                    String testName = testMatcher.group(2).trim();
                    String testDataSuggestion = testMatcher.group(3);

                    // Create test object
                    ObjectNode test = objectMapper.createObjectNode();
                    test.put("id", testIdCounter++);
                    test.put("name", testName);
                    test.put("priority", priority);
                    test.put("testDataSuggestion", testDataSuggestion != null ? testDataSuggestion : "");
                    test.put("status", "pending");

                    // Add to current subcategory or create default one
                    if (currentSubcategory == null && currentCategory != null) {
                        // Create default subcategory
                        currentSubcategory = objectMapper.createObjectNode();
                        String subcategoryId = currentCategory.get("id").asText() + ".1";
                        currentSubcategory.put("id", subcategoryId);
                        currentSubcategory.put("name", "General Tests");
                        currentSubcategory.put("total_checks", 0);
                        currentTests = objectMapper.createArrayNode();
                        currentSubcategory.set("tests", currentTests);

                        ((ArrayNode) currentCategory.get("subcategories")).add(currentSubcategory);
                    }

                    if (currentTests != null) {
                        currentTests.add(test);
                        totalChecks++;
                    }
                }
            }

            // Finish last category and subcategory
            finishCategory(currentCategory, currentSubcategory);

            // Calculate totals
            calculateTotals(categories);

            // Build final structure
            testChecklist.put("title", title);
            testChecklist.put("total_checks", totalChecks);
            testChecklist.set("categories", categories);

            root.set("test_checklist", testChecklist);

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(root);

        } catch (Exception e) {
            e.printStackTrace();
            return "{}";
        }
    }

    private void finishCategory(ObjectNode category, ObjectNode subcategory) {
        if (category != null) {
            finishSubcategory(subcategory);
        }
    }

    private void finishSubcategory(ObjectNode subcategory) {
        if (subcategory != null) {
            ArrayNode tests = (ArrayNode) subcategory.get("tests");
            if (tests != null) {
                subcategory.put("total_checks", tests.size());
            }
        }
    }

    private void calculateTotals(ArrayNode categories) {
        int totalChecks = 0;

        for (int i = 0; i < categories.size(); i++) {
            ObjectNode category = (ObjectNode) categories.get(i);
            ArrayNode subcategories = (ArrayNode) category.get("subcategories");
            int categoryTotal = 0;

            for (int j = 0; j < subcategories.size(); j++) {
                ObjectNode subcategory = (ObjectNode) subcategories.get(j);
                int subcategoryTotal = subcategory.get("total_checks").asInt();
                categoryTotal += subcategoryTotal;
            }

            category.put("total_checks", categoryTotal);
            totalChecks += categoryTotal;
        }

        // Update total in root if needed
        if (categories.size() > 0) {
            ObjectNode firstCategory = (ObjectNode) categories.get(0);
            // Note: total_checks will be set in the main method
        }
    }

    public String parseTestCasesToJson(String content) {
        try {
            ObjectNode root = objectMapper.createObjectNode();
            ArrayNode testCases = objectMapper.createArrayNode();

            String[] sections = content.split("(?=### \\*\\*Test Case:)");

            int totalTestCases = 0;
            String category = "Generated Test Cases";
            String platform = "General";

            for (String section : sections) {
                section = section.trim();
                if (section.isEmpty()) continue;

                ObjectNode testCase = parseTestCaseSection(section);
                if (testCase != null) {
                    testCases.add(testCase);
                    totalTestCases++;

                    // Extract category and platform from first test case if available
                    if (totalTestCases == 1) {
                        String description = testCase.get("description").asText();
                        if (description.toLowerCase().contains("apple") || description.toLowerCase().contains("ios")) {
                            platform = "Apple iOS";
                        }
                        if (description.toLowerCase().contains("payment") || description.toLowerCase().contains("iap")) {
                            category = "In-App Purchase (IAP)";
                        }
                    }
                }
            }

            // Build metadata
            ObjectNode metadata = objectMapper.createObjectNode();
            metadata.put("totalTestCases", totalTestCases);
            metadata.put("category", category);
            metadata.put("platform", platform);
            metadata.put("generatedDate", java.time.LocalDate.now().toString());
            metadata.put("format", "JSON");

            root.set("testCases", testCases);
            root.set("metadata", metadata);

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(root);

        } catch (Exception e) {
            e.printStackTrace();
            return "{}";
        }
    }

    private ObjectNode parseTestCaseSection(String section) {
        try {
            ObjectNode testCase = objectMapper.createObjectNode();
            String[] lines = section.split("\n");

            String id = "";
            String title = "";
            StringBuilder description = new StringBuilder();
            StringBuilder source = new StringBuilder();
            ArrayNode preconditions = objectMapper.createArrayNode();
            ArrayNode steps = objectMapper.createArrayNode();
            StringBuilder additionalNotes = new StringBuilder();

            boolean inDescription = false;
            boolean inSource = false;
            boolean inPreconditions = false;
            boolean inSteps = false;
            boolean inNotes = false;
            int stepCounter = 1;

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.equals("---")) continue;

                // Extract test case ID and title
                if (line.startsWith("### **Test Case:") && line.contains("-")) {
                    String titleLine = line.substring(16).trim(); // Remove "### **Test Case:"
                    if (titleLine.endsWith("**")) {
                        titleLine = titleLine.substring(0, titleLine.length() - 2);
                    }

                    String[] parts = titleLine.split(" - ", 2);
                    if (parts.length >= 2) {
                        id = parts[0].trim();
                        title = parts[1].trim();
                    }
                    resetFlags();
                    continue;
                }

                // Extract description
                if (line.startsWith("**Description:**")) {
                    String desc = line.substring(16).trim();
                    description.setLength(0);
                    description.append(desc);
                    inDescription = true;
                    inSource = false;
                    inPreconditions = false;
                    inSteps = false;
                    inNotes = false;
                    continue;
                } else if (inDescription && !line.startsWith("**")) {
                    description.append(" ").append(line);
                    continue;
                }

                // Extract source
                if (line.startsWith("**Source (from Checklist):**")) {
                    String src = "";
                    if (line.length() > 29) {
                        src = line.substring(29).trim();
                    }
                    source.setLength(0);
                    source.append(src);
                    inDescription = false;
                    inSource = true;
                    inPreconditions = false;
                    inSteps = false;
                    inNotes = false;
                    continue;
                } else if (inSource && !line.startsWith("**") && !line.startsWith("---")) {
                    source.append(" ").append(line);
                    continue;
                }

                // Section markers
                if (line.equals("**Preconditions:**")) {
                    inDescription = false;
                    inSource = false;
                    inPreconditions = true;
                    inSteps = false;
                    inNotes = false;
                    continue;
                }

                if (line.equals("**Steps:**")) {
                    inDescription = false;
                    inSource = false;
                    inPreconditions = false;
                    inSteps = true;
                    inNotes = false;
                    stepCounter = 1;
                    continue;
                }

                if (line.startsWith("**Additional Notes")) {
                    inDescription = false;
                    inSource = false;
                    inPreconditions = false;
                    inSteps = false;
                    inNotes = true;
                    int colonIndex = line.indexOf(":");
                    if (colonIndex != -1 && colonIndex + 1 < line.length()) {
                        additionalNotes.setLength(0);
                        additionalNotes.append(line.substring(colonIndex + 1).trim());
                    }
                    continue;
                }

                // Parse preconditions
                if (inPreconditions && line.startsWith("*")) {
                    String precondition = line.substring(1).trim();
                    preconditions.add(precondition);
                    continue;
                }

                // Parse steps - handle the new format
                if (inSteps) {
                    if (line.matches("^\\d+\\..*")) {
                        // New step starting - extract action from the same line if present
                        ObjectNode step = objectMapper.createObjectNode();
                        step.put("stepNumber", stepCounter++);

                        // Check if action is on the same line after "**Action:**"
                        if (line.contains("**Action:**")) {
                            int actionIndex = line.indexOf("**Action:**");
                            if (actionIndex != -1) {
                                String action = line.substring(actionIndex + 11).trim();
                                step.put("action", action);
                            }
                        }

                        steps.add(step);
                        continue;
                    }

                    if (!steps.isEmpty()) {
                        ObjectNode currentStep = (ObjectNode) steps.get(steps.size() - 1);

                        if (line.startsWith("**Action:**")) {
                            String action = line.substring(11).trim();
                            currentStep.put("action", action);
                        } else if (line.startsWith("**Expected Result:**")) {
                            String expectedResult = line.substring(20).trim();
                            currentStep.put("expectedResult", expectedResult);
                        } else if (line.startsWith("**Test Data:**")) {
                            String testData = line.substring(14).trim();
                            currentStep.put("testData", testData);
                        } else if (line.startsWith("*") && !line.startsWith("**")) {
                            // Handle bullet point test data
                            String existingTestData = currentStep.has("testData") ? currentStep.get("testData").asText() : "";
                            String newTestData = line.substring(1).trim();
                            if (!existingTestData.isEmpty() && !existingTestData.equals("N/A")) {
                                currentStep.put("testData", existingTestData + "; " + newTestData);
                            } else {
                                currentStep.put("testData", newTestData);
                            }
                        }
                    }
                    continue;
                }

                // Additional notes continuation
                if (inNotes && !line.startsWith("---")) {
                    if (line.startsWith("*") && !line.startsWith("**")) {
                        // Handle bullet points in additional notes
                        String note = line.substring(1).trim();
                        if (additionalNotes.length() > 0) {
                            additionalNotes.append("; ").append(note);
                        } else {
                            additionalNotes.append(note);
                        }
                    } else if (!line.startsWith("*")) {
                        // Handle regular text continuation
                        if (additionalNotes.length() > 0) {
                            additionalNotes.append(" ").append(line);
                        } else {
                            additionalNotes.append(line);
                        }
                    }
                }
            }

            // Build final test case object
            if (!id.isEmpty()) {
                testCase.put("id", id);
                testCase.put("title", title);
                testCase.put("description", description.toString().trim());

                // Clean up source - remove quotes if present
                String sourceStr = source.toString().trim();
                if (sourceStr.startsWith("\"") && sourceStr.endsWith("\"") && sourceStr.length() > 1) {
                    sourceStr = sourceStr.substring(1, sourceStr.length() - 1);
                }
                testCase.put("source", sourceStr);

                testCase.set("preconditions", preconditions);
                testCase.set("steps", steps);
                testCase.put("additionalNotes", additionalNotes.toString().trim());

                return testCase;
            }

            return null;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private void resetFlags() {
        // Helper method to reset all flags
    }
} 