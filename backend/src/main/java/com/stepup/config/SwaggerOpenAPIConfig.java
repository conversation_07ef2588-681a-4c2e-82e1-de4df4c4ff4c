package com.stepup.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class SwaggerOpenAPIConfig {
    @Value("${swagger.openapi.prod-url}")
    private String productionUrl;

    @Value("${swagger.openapi.staging-url}")
    private String stagingUrl;

    @Value("${swagger.openapi.dev-url}")
    private String developmentUrl;

    @Value("${swagger.openapi.local-url}")
    private String localUrl;

    private SecurityScheme createAPIKeyScheme() {
        return new SecurityScheme().type(SecurityScheme.Type.HTTP)
                .bearerFormat("JWT")
                .scheme("bearer");
    }

    @Bean
    public OpenAPI myOpenAPI() {
        List<Server> servers = new ArrayList<>();
        if (StringUtils.hasText(localUrl)) {
            Server localServer = new Server();
            localServer.setUrl(localUrl);
            localServer.setDescription("Server URL in Local environment");
            servers.add(localServer);
        }

        if (StringUtils.hasText(productionUrl)) {
            Server productionServer = new Server();
            productionServer.setUrl(productionUrl);
            productionServer.setDescription("Server URL in Production environment");
            servers.add(productionServer);
        }

        if (StringUtils.hasText(stagingUrl)) {
            Server stagingServer = new Server();
            stagingServer.setUrl(stagingUrl);
            stagingServer.setDescription("Server URL in Staging environment");
            servers.add(stagingServer);
        }

        if (StringUtils.hasText(developmentUrl)) {
            Server devServer = new Server();
            devServer.setUrl(developmentUrl);
            devServer.setDescription("Server URL in Development environment");
            servers.add(devServer);
        }

        Contact contact = new Contact();
        contact.setEmail("<EMAIL>");
        contact.setName("TestAngel Team");

        Info info = new Info()
                .title("TestAngel Management API")
                .version("1.0")
                .contact(contact)
                .description("This API exposes endpoints to manage TestAngel Project.");

        return new OpenAPI().info(info)
                .servers(servers)
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new Components().addSecuritySchemes("Bearer Authentication", createAPIKeyScheme()));
    }
} 